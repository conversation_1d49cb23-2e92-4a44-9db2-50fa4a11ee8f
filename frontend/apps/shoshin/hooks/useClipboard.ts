"use client"

import { useCallback, useRef } from "react"
import type { Node, <PERSON> } from "@xyflow/react"

interface ClipboardData {
  nodes: Node[]
  edges: Edge[]
  timestamp: number
}

interface ClipboardActions {
  copy: (nodes: Node[], edges: Edge[]) => void
  cut: (nodes: Node[], edges: Edge[], onDelete: (nodeIds: string[], edgeIds: string[]) => void) => void
  paste: (position?: { x: number; y: number }) => { nodes: Node[]; edges: Edge[] } | null
  hasClipboardData: boolean
}

export function useClipboard(): ClipboardActions {
  const clipboardData = useRef<ClipboardData | null>(null)

  const generateUniqueId = useCallback((prefix: string): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }, [])

  const copy = useCallback((nodes: Node[], edges: Edge[]) => {
    if (nodes.length === 0 && edges.length === 0) return

    // Get all edges that connect the selected nodes
    const nodeIds = new Set(nodes.map(node => node.id))
    const relevantEdges = edges.filter(edge => 
      nodeIds.has(edge.source) && nodeIds.has(edge.target)
    )

    clipboardData.current = {
      nodes: JSON.parse(JSON.stringify(nodes)),
      edges: JSON.parse(JSON.stringify(relevantEdges)),
      timestamp: Date.now()
    }
  }, [])

  const cut = useCallback((
    nodes: Node[], 
    edges: Edge[], 
    onDelete: (nodeIds: string[], edgeIds: string[]) => void
  ) => {
    copy(nodes, edges)
    
    const nodeIds = nodes.map(node => node.id)
    const edgeIds = edges.map(edge => edge.id)
    
    onDelete(nodeIds, edgeIds)
  }, [copy])

  const paste = useCallback((position?: { x: number; y: number }) => {
    if (!clipboardData.current) return null

    const { nodes: clipboardNodes, edges: clipboardEdges } = clipboardData.current
    
    if (clipboardNodes.length === 0) return null

    // Calculate offset for pasting
    const defaultPosition = position || { x: 100, y: 100 }
    
    // Find the top-left position of copied nodes to calculate offset
    const minX = Math.min(...clipboardNodes.map(node => node.position.x))
    const minY = Math.min(...clipboardNodes.map(node => node.position.y))
    
    const offsetX = defaultPosition.x - minX
    const offsetY = defaultPosition.y - minY

    // Create mapping from old IDs to new IDs
    const idMapping = new Map<string, string>()
    
    // Create new nodes with unique IDs and adjusted positions
    const newNodes: Node[] = clipboardNodes.map(node => {
      const newId = generateUniqueId(node.data.type || 'node')
      idMapping.set(node.id, newId)
      
      return {
        ...node,
        id: newId,
        position: {
          x: node.position.x + offsetX,
          y: node.position.y + offsetY
        },
        selected: false // Don't select pasted nodes by default
      }
    })

    // Create new edges with updated node references
    const newEdges: Edge[] = clipboardEdges
      .filter(edge => idMapping.has(edge.source) && idMapping.has(edge.target))
      .map(edge => ({
        ...edge,
        id: generateUniqueId('edge'),
        source: idMapping.get(edge.source)!,
        target: idMapping.get(edge.target)!,
        selected: false
      }))

    return { nodes: newNodes, edges: newEdges }
  }, [generateUniqueId])

  return {
    copy,
    cut,
    paste,
    hasClipboardData: clipboardData.current !== null
  }
}
