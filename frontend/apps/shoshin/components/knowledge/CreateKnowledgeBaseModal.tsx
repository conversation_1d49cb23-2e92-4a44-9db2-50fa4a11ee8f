"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useKnowledgeStore } from "@/stores/knowledgeStore"
import { X, Upload } from "lucide-react"

const formSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
  minChunkSize: z.number().min(50, "Minimum chunk size must be at least 50").max(2000, "Minimum chunk size must be less than 2000"),
  maxChunkSize: z.number().min(100, "Maximum chunk size must be at least 100").max(5000, "Maximum chunk size must be less than 5000"),
  overlapSize: z.number().min(0, "Overlap size must be non-negative").max(500, "Overlap size must be less than 500"),
})

interface CreateKnowledgeBaseModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CreateKnowledgeBaseModal({ isOpen, onClose }: CreateKnowledgeBaseModalProps) {
  const { createKnowledgeBase, isLoading } = useKnowledgeStore()
  const [files, setFiles] = useState<File[]>([])

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      description: "",
      minChunkSize: 100,
      maxChunkSize: 1024,
      overlapSize: 200,
    },
  })

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    setFiles(selectedFiles)
  }

  const removeFile = (index: number) => {
    setFiles(files.filter((_, i) => i !== index))
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    try {
      // Validate chunk sizes
      if (values.minChunkSize >= values.maxChunkSize) {
        form.setError("maxChunkSize", {
          message: "Maximum chunk size must be greater than minimum chunk size"
        })
        return
      }

      if (values.overlapSize >= values.minChunkSize) {
        form.setError("overlapSize", {
          message: "Overlap size must be less than minimum chunk size"
        })
        return
      }

      const knowledgeBaseId = await createKnowledgeBase(values)
      
      // TODO: Upload files to the created knowledge base
      // This would be implemented when we have the file upload functionality
      
      form.reset()
      setFiles([])
      onClose()
    } catch (error) {
      console.error("Failed to create knowledge base:", error)
    }
  }

  const handleClose = () => {
    form.reset()
    setFiles([])
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Knowledge Base</DialogTitle>
          <DialogDescription>
            Create a new knowledge base to store and organize your documents.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Name Field */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Name *</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter knowledge base name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Description Field */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe what this knowledge base contains (optional)"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Chunking Configuration */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Chunking Configuration</h4>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="minChunkSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Min Chunk Size</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="maxChunkSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Max Chunk Size</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="overlapSize"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Overlap Size</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormDescription>
                      Smaller chunks provide more precise context. Larger chunks provide more context.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* File Upload Section */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Upload Documents</h4>
              
              <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Drop files here or click to browse
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-500">
                  Supports PDF, DOCX, TXT, CSV, XLS, XLSX (max 100MB each)
                </p>
                <input
                  type="file"
                  multiple
                  accept=".pdf,.docx,.txt,.csv,.xls,.xlsx"
                  onChange={handleFileChange}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload">
                  <Button type="button" variant="outline" className="mt-2" asChild>
                    <span>Choose Files</span>
                  </Button>
                </label>
              </div>

              {/* Selected Files */}
              {files.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium">Selected Files:</p>
                  {files.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={handleClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading} className="bg-purple-600 hover:bg-purple-700">
                {isLoading ? "Creating..." : "Create Knowledge Base"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
