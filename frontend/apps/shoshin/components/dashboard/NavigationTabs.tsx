"use client"

import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface Tab {
  id: string
  label: string
  count?: number
}

interface NavigationTabsProps {
  tabs: Tab[]
  activeTab: string
  onTabChange: (tabId: string) => void
  className?: string
}

export function NavigationTabs({ tabs, activeTab, onTabChange, className }: NavigationTabsProps) {
  return (
    <div className={cn("border-b border-border/50", className)}>
      <nav className="-mb-px flex space-x-8">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={cn(
              "border-b-2 py-3 px-2 text-sm font-medium transition-all duration-200 flex items-center gap-3 rounded-t-creative-sm",
              activeTab === tab.id
                ? "border-primary text-primary bg-gradient-primary-soft"
                : "border-transparent text-muted-foreground hover:text-foreground hover:border-primary/30 hover:bg-muted/50"
            )}
          >
            {tab.label}
            {tab.count !== undefined && (
              <Badge
                variant={activeTab === tab.id ? "default" : "outline"}
                className="text-xs"
              >
                {tab.count}
              </Badge>
            )}
          </button>
        ))}
      </nav>
    </div>
  )
}
