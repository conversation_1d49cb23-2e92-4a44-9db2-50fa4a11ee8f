"use client"

import { useState } from "react"
import { MessageSquare, Terminal, Variable, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

const tabs = [
  { id: "chat", label: "Chat", icon: MessageSquare },
  { id: "console", label: "Console", icon: Terminal },
  { id: "variables", label: "Variables", icon: Variable },
]

export function RightSidebar() {
  const [activeTab, setActiveTab] = useState("chat")
  const [isCollapsed, setIsCollapsed] = useState(false)

  if (isCollapsed) {
    return (
      <div className="w-12 bg-gray-800 border-l border-gray-700 flex flex-col items-center py-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(false)}
          className="text-gray-400 hover:text-white"
        >
          <MessageSquare className="w-4 h-4" />
        </Button>
      </div>
    )
  }

  return (
    <div className="w-80 bg-gray-800 border-l border-gray-700 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700">
        <div className="text-sm font-medium text-white">Select output sources</div>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(true)}
          className="text-gray-400 hover:text-white"
        >
          <X className="w-4 h-4" />
        </Button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-gray-700">
        {tabs.map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id)}
            className={`flex-1 flex items-center justify-center px-4 py-3 text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? "text-white bg-gray-700 border-b-2 border-purple-500"
                : "text-gray-400 hover:text-white"
            }`}
          >
            <tab.icon className="w-4 h-4 mr-2" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex-1 p-4">
        {activeTab === "chat" && (
          <div className="h-full flex flex-col">
            <div className="flex-1 flex items-center justify-center text-gray-400 text-sm">
              No messages yet
            </div>
            <div className="mt-4">
              <input
                type="text"
                placeholder="Type a message..."
                className="w-full bg-gray-700 text-white placeholder-gray-400 px-3 py-2 rounded-md border border-gray-600 focus:border-purple-500 focus:outline-none text-sm"
              />
            </div>
          </div>
        )}

        {activeTab === "console" && (
          <div className="h-full">
            <div className="text-gray-400 text-sm">Console output will appear here...</div>
          </div>
        )}

        {activeTab === "variables" && (
          <div className="h-full">
            <div className="text-gray-400 text-sm">Variables will be listed here...</div>
          </div>
        )}
      </div>
    </div>
  )
}
