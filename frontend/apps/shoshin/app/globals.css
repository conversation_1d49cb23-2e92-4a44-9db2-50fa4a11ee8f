@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Shoshin Creative Design System - Light Theme */

    /* Primary Brand Colors - Purple to Pink Gradient */
    --primary: 262 83% 58%; /* #8B5CF6 - Vibrant Purple */
    --primary-foreground: 0 0% 100%;
    --primary-light: 270 91% 65%; /* #A855F7 - Lighter Purple */
    --primary-dark: 258 90% 66%; /* #7C3AED - Darker Purple */

    /* Secondary Brand Colors */
    --secondary: 329 86% 70%; /* #EC4899 - Vibrant Pink */
    --secondary-foreground: 0 0% 100%;
    --secondary-light: 330 81% 76%; /* #F472B6 - Light Pink */
    --secondary-dark: 328 85% 64%; /* #E879F9 - Dark Pink */

    /* Accent Colors */
    --accent-orange: 24 95% 53%; /* #F97316 - Creative Orange */
    --accent-orange-foreground: 0 0% 100%;
    --accent-blue: 217 91% 60%; /* #3B82F6 - Professional Blue */
    --accent-blue-foreground: 0 0% 100%;

    /* Success, Warning, Error */
    --success: 142 76% 36%; /* #10B981 - Success Green */
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%; /* #F59E0B - Warning Amber */
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84% 60%; /* #EF4444 - Error Red */
    --destructive-foreground: 0 0% 100%;

    /* Neutral Colors */
    --background: 0 0% 100%; /* Pure White */
    --foreground: 224 71% 4%; /* #020617 - Rich Dark */
    --card: 0 0% 100%;
    --card-foreground: 224 71% 4%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71% 4%;
    --muted: 220 14% 96%; /* #F8FAFC - Light Gray */
    --muted-foreground: 220 9% 46%; /* #64748B - Medium Gray */
    --border: 220 13% 91%; /* #E2E8F0 - Light Border */
    --input: 220 13% 91%;
    --ring: 262 83% 58%; /* Primary color for focus rings */

    /* Design System Variables */
    --radius: 0.75rem; /* 12px default radius */
    --radius-sm: 0.375rem; /* 6px small radius */
    --radius-lg: 1.25rem; /* 20px large radius */
    --radius-xl: 1.5rem; /* 24px extra large radius */
  }

  .dark {
    /* Shoshin Creative Design System - Dark Theme */

    /* Primary Brand Colors - Adjusted for dark theme */
    --primary: 262 80% 65%; /* #A855F7 - Slightly lighter for dark */
    --primary-foreground: 224 71% 4%;
    --primary-light: 270 87% 70%; /* #B47EF7 */
    --primary-dark: 258 85% 60%; /* #8B5CF6 */

    /* Secondary Brand Colors */
    --secondary: 329 81% 76%; /* #F472B6 - Lighter pink for dark */
    --secondary-foreground: 224 71% 4%;
    --secondary-light: 330 76% 80%; /* #F9A8D4 */
    --secondary-dark: 328 80% 70%; /* #EC4899 */

    /* Accent Colors */
    --accent-orange: 24 90% 58%; /* #FB923C - Slightly muted */
    --accent-orange-foreground: 224 71% 4%;
    --accent-blue: 217 85% 65%; /* #60A5FA - Lighter blue */
    --accent-blue-foreground: 224 71% 4%;

    /* Success, Warning, Error */
    --success: 142 71% 45%; /* #34D399 - Lighter green */
    --success-foreground: 224 71% 4%;
    --warning: 38 87% 55%; /* #FBBF24 - Lighter amber */
    --warning-foreground: 224 71% 4%;
    --destructive: 0 79% 65%; /* #F87171 - Lighter red */
    --destructive-foreground: 224 71% 4%;

    /* Neutral Colors */
    --background: 224 71% 4%; /* #020617 - Rich Dark Blue */
    --foreground: 210 40% 98%; /* #F8FAFC - Off White */
    --card: 224 71% 6%; /* #0F172A - Slightly lighter than bg */
    --card-foreground: 210 40% 98%;
    --popover: 224 71% 6%;
    --popover-foreground: 210 40% 98%;
    --muted: 215 28% 17%; /* #1E293B - Dark Muted */
    --muted-foreground: 217 11% 65%; /* #94A3B8 - Light Gray */
    --border: 215 28% 17%; /* #1E293B - Dark Border */
    --input: 215 28% 17%;
    --ring: 262 80% 65%; /* Primary color for focus rings */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    font-family:
      var(--font-geist-sans),
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Roboto,
      sans-serif;
  }

  code,
  pre {
    @apply font-mono;
  }
}

/* ReactFlow edge animations */
@keyframes dash {
  to {
    stroke-dashoffset: 10;
  }
}

.react-flow__edge-path {
  animation: dash 1s linear infinite;
}

/* Sidebar overlay styles */
.main-content-overlay {
  z-index: 40 !important;
  /* Higher z-index to appear above content */
}

@layer components {
  /* Gradient Utilities */
  .gradient-primary {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 100%
    );
  }

  .gradient-primary-soft {
    background: linear-gradient(
      135deg,
      hsl(var(--primary) / 0.1) 0%,
      hsl(var(--secondary) / 0.1) 100%
    );
  }

  .gradient-accent {
    background: linear-gradient(
      135deg,
      hsl(var(--accent-blue)) 0%,
      hsl(var(--accent-orange)) 100%
    );
  }

  .gradient-text {
    background: linear-gradient(
      135deg,
      hsl(var(--primary)) 0%,
      hsl(var(--secondary)) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Glass Morphism Effects */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Enhanced Shadows */
  .shadow-glow {
    box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
  }

  .shadow-glow-secondary {
    box-shadow: 0 0 20px hsl(var(--secondary) / 0.3);
  }

  .shadow-creative {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      0 0 0 1px hsl(var(--primary) / 0.1);
  }

  .shadow-creative-lg {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px hsl(var(--primary) / 0.1);
  }

  /* Interactive Elements */
  .interactive-scale {
    transition: transform 0.2s ease-in-out;
  }

  .interactive-scale:hover {
    transform: scale(1.02);
  }

  .interactive-glow {
    transition: box-shadow 0.3s ease-in-out;
  }

  .interactive-glow:hover {
    box-shadow: 0 0 30px hsl(var(--primary) / 0.4);
  }
}

@layer utilities {
  /* Border Radius Utilities */
  .rounded-creative {
    border-radius: var(--radius);
  }

  .rounded-creative-sm {
    border-radius: var(--radius-sm);
  }

  .rounded-creative-lg {
    border-radius: var(--radius-lg);
  }

  .rounded-creative-xl {
    border-radius: var(--radius-xl);
  }

  /* Background Patterns */
  .bg-grid {
    background-image:
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .bg-dots {
    background-image: radial-gradient(
      circle,
      rgba(0, 0, 0, 0.1) 1px,
      transparent 1px
    );
    background-size: 20px 20px;
  }
}
