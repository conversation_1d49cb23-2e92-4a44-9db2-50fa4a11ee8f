"use client"

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'

export interface KnowledgeDocument {
  id: string
  name: string
  size: number
  tokens: number
  chunks: number
  uploaded: string // ISO date string
  status: 'processing' | 'ready' | 'error'
  type: string // file extension or type
}

export interface KnowledgeBase {
  id: string
  name: string
  description?: string
  minChunkSize: number
  maxChunkSize: number
  overlapSize: number
  documents: KnowledgeDocument[]
  createdAt: string // ISO date string
  updatedAt: string // ISO date string
}

interface KnowledgeState {
  // State
  knowledgeBases: Record<string, KnowledgeBase>
  selectedKnowledgeBaseId: string | null
  isLoading: boolean
  error: string | null
  
  // Search and filters
  searchQuery: string
  
  // Modal states
  isCreateModalOpen: boolean
  isDeleteModalOpen: boolean
  knowledgeBaseToDelete: string | null
  
  // Actions - Knowledge Base Management
  createKnowledgeBase: (data: {
    name: string
    description?: string
    minChunkSize: number
    maxChunkSize: number
    overlapSize: number
  }) => Promise<string>
  updateKnowledgeBase: (id: string, updates: Partial<KnowledgeBase>) => void
  deleteKnowledgeBase: (id: string) => Promise<void>
  getKnowledgeBase: (id: string) => KnowledgeBase | null
  getAllKnowledgeBases: () => KnowledgeBase[]
  
  // Actions - Document Management
  addDocument: (knowledgeBaseId: string, file: File) => Promise<string>
  removeDocument: (knowledgeBaseId: string, documentId: string) => Promise<void>
  updateDocumentStatus: (knowledgeBaseId: string, documentId: string, status: KnowledgeDocument['status']) => void
  
  // Actions - UI State
  setSelectedKnowledgeBase: (id: string | null) => void
  setSearchQuery: (query: string) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Actions - Modal Management
  openCreateModal: () => void
  closeCreateModal: () => void
  openDeleteModal: (knowledgeBaseId: string) => void
  closeDeleteModal: () => void
  
  // Actions - Search and Filter
  searchKnowledgeBases: (query: string) => KnowledgeBase[]
  searchDocuments: (knowledgeBaseId: string, query: string) => KnowledgeDocument[]
}

const generateId = (): string => {
  return `kb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

const generateDocumentId = (): string => {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Mock API functions - replace with actual API calls
const mockApiDelay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms))

export const useKnowledgeStore = create<KnowledgeState>()(
  subscribeWithSelector((set, get) => ({
    // Initial state
    knowledgeBases: {},
    selectedKnowledgeBaseId: null,
    isLoading: false,
    error: null,
    searchQuery: '',
    isCreateModalOpen: false,
    isDeleteModalOpen: false,
    knowledgeBaseToDelete: null,

    // Knowledge Base Management
    createKnowledgeBase: async (data) => {
      set({ isLoading: true, error: null })
      
      try {
        await mockApiDelay()
        
        const id = generateId()
        const now = new Date().toISOString()
        
        const newKnowledgeBase: KnowledgeBase = {
          id,
          name: data.name,
          description: data.description,
          minChunkSize: data.minChunkSize,
          maxChunkSize: data.maxChunkSize,
          overlapSize: data.overlapSize,
          documents: [],
          createdAt: now,
          updatedAt: now,
        }
        
        set(state => ({
          knowledgeBases: {
            ...state.knowledgeBases,
            [id]: newKnowledgeBase
          },
          isLoading: false,
          isCreateModalOpen: false
        }))
        
        return id
      } catch (error) {
        set({ 
          error: error instanceof Error ? error.message : 'Failed to create knowledge base',
          isLoading: false 
        })
        throw error
      }
    },

    updateKnowledgeBase: (id, updates) => {
      set(state => {
        const knowledgeBase = state.knowledgeBases[id]
        if (!knowledgeBase) return state
        
        return {
          knowledgeBases: {
            ...state.knowledgeBases,
            [id]: {
              ...knowledgeBase,
              ...updates,
              updatedAt: new Date().toISOString()
            }
          }
        }
      })
    },

    deleteKnowledgeBase: async (id) => {
      set({ isLoading: true, error: null })
      
      try {
        await mockApiDelay()
        
        set(state => {
          const { [id]: deleted, ...remaining } = state.knowledgeBases
          return {
            knowledgeBases: remaining,
            selectedKnowledgeBaseId: state.selectedKnowledgeBaseId === id ? null : state.selectedKnowledgeBaseId,
            isLoading: false,
            isDeleteModalOpen: false,
            knowledgeBaseToDelete: null
          }
        })
      } catch (error) {
        set({ 
          error: error instanceof Error ? error.message : 'Failed to delete knowledge base',
          isLoading: false 
        })
        throw error
      }
    },

    getKnowledgeBase: (id) => {
      return get().knowledgeBases[id] || null
    },

    getAllKnowledgeBases: () => {
      return Object.values(get().knowledgeBases)
    },

    // Document Management
    addDocument: async (knowledgeBaseId, file) => {
      const knowledgeBase = get().knowledgeBases[knowledgeBaseId]
      if (!knowledgeBase) {
        throw new Error('Knowledge base not found')
      }
      
      set({ isLoading: true, error: null })
      
      try {
        await mockApiDelay(1000) // Simulate upload time
        
        const documentId = generateDocumentId()
        const now = new Date().toISOString()
        
        const newDocument: KnowledgeDocument = {
          id: documentId,
          name: file.name,
          size: file.size,
          tokens: Math.floor(file.size / 4), // Rough estimate: 1 token ≈ 4 characters
          chunks: Math.ceil(file.size / 1000), // Rough estimate
          uploaded: now,
          status: 'processing',
          type: file.name.split('.').pop() || 'unknown'
        }
        
        set(state => ({
          knowledgeBases: {
            ...state.knowledgeBases,
            [knowledgeBaseId]: {
              ...state.knowledgeBases[knowledgeBaseId],
              documents: [...state.knowledgeBases[knowledgeBaseId].documents, newDocument],
              updatedAt: now
            }
          },
          isLoading: false
        }))
        
        // Simulate processing completion
        setTimeout(() => {
          get().updateDocumentStatus(knowledgeBaseId, documentId, 'ready')
        }, 2000)
        
        return documentId
      } catch (error) {
        set({ 
          error: error instanceof Error ? error.message : 'Failed to upload document',
          isLoading: false 
        })
        throw error
      }
    },

    removeDocument: async (knowledgeBaseId, documentId) => {
      set({ isLoading: true, error: null })
      
      try {
        await mockApiDelay()
        
        set(state => {
          const knowledgeBase = state.knowledgeBases[knowledgeBaseId]
          if (!knowledgeBase) return state
          
          return {
            knowledgeBases: {
              ...state.knowledgeBases,
              [knowledgeBaseId]: {
                ...knowledgeBase,
                documents: knowledgeBase.documents.filter(doc => doc.id !== documentId),
                updatedAt: new Date().toISOString()
              }
            },
            isLoading: false
          }
        })
      } catch (error) {
        set({ 
          error: error instanceof Error ? error.message : 'Failed to remove document',
          isLoading: false 
        })
        throw error
      }
    },

    updateDocumentStatus: (knowledgeBaseId, documentId, status) => {
      set(state => {
        const knowledgeBase = state.knowledgeBases[knowledgeBaseId]
        if (!knowledgeBase) return state
        
        return {
          knowledgeBases: {
            ...state.knowledgeBases,
            [knowledgeBaseId]: {
              ...knowledgeBase,
              documents: knowledgeBase.documents.map(doc =>
                doc.id === documentId ? { ...doc, status } : doc
              ),
              updatedAt: new Date().toISOString()
            }
          }
        }
      })
    },

    // UI State Management
    setSelectedKnowledgeBase: (id) => set({ selectedKnowledgeBaseId: id }),
    setSearchQuery: (query) => set({ searchQuery: query }),
    setLoading: (loading) => set({ isLoading: loading }),
    setError: (error) => set({ error }),

    // Modal Management
    openCreateModal: () => set({ isCreateModalOpen: true }),
    closeCreateModal: () => set({ isCreateModalOpen: false }),
    openDeleteModal: (knowledgeBaseId) => set({ 
      isDeleteModalOpen: true, 
      knowledgeBaseToDelete: knowledgeBaseId 
    }),
    closeDeleteModal: () => set({ 
      isDeleteModalOpen: false, 
      knowledgeBaseToDelete: null 
    }),

    // Search and Filter
    searchKnowledgeBases: (query) => {
      const { knowledgeBases } = get()
      const lowerQuery = query.toLowerCase()
      
      return Object.values(knowledgeBases).filter(kb =>
        kb.name.toLowerCase().includes(lowerQuery) ||
        (kb.description && kb.description.toLowerCase().includes(lowerQuery))
      )
    },

    searchDocuments: (knowledgeBaseId, query) => {
      const knowledgeBase = get().knowledgeBases[knowledgeBaseId]
      if (!knowledgeBase) return []
      
      const lowerQuery = query.toLowerCase()
      return knowledgeBase.documents.filter(doc =>
        doc.name.toLowerCase().includes(lowerQuery)
      )
    }
  }))
)
