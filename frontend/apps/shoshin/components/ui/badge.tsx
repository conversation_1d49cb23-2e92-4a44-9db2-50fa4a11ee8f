import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "@/lib/utils"

const badgeVariants = cva(
  "inline-flex items-center rounded-creative-sm border px-3 py-1 text-xs font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-gradient-primary text-primary-foreground shadow-creative hover:shadow-glow hover:scale-105",
        secondary:
          "border-transparent bg-secondary text-secondary-foreground shadow-creative hover:bg-secondary/80 hover:shadow-glow-secondary",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow-creative hover:bg-destructive/80",
        outline: "text-foreground border-primary/20 hover:bg-gradient-primary-soft hover:border-primary/50",
        success: "border-transparent bg-success text-success-foreground shadow-creative hover:bg-success/80",
        warning: "border-transparent bg-warning text-warning-foreground shadow-creative hover:bg-warning/80",
        accent: "border-transparent bg-gradient-accent text-white shadow-creative hover:shadow-lg hover:scale-105",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
)

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, ...props }: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props} />
  )
}

export { Badge, badgeVariants }
