"use client"

import { MetricsGrid } from "@/components/dashboard/MetricCard"
import { NavigationTabs } from "@/components/dashboard/NavigationTabs"
import { WelcomeSection } from "@/components/dashboard/WelcomeSection"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useState } from "react"

export default function DashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("workflows")
  const [activeMetric, setActiveMetric] = useState<string>()

  const tabs = [
    { id: "workflows", label: "Workflows", count: 0 },
    { id: "credentials", label: "Credentials", count: 0 },
    { id: "executions", label: "Executions", count: 0 }
  ]

  const handleCreateWorkflow = () => {
    router.push("/editor")
  }

  const handleMetricClick = (metric: string) => {
    setActiveMetric(metric)
    // Add metric filtering logic here
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/10">
      {/* Header */}
      <div className="border-b border-border/50 bg-card/80 backdrop-blur-sm shadow-creative">
        <div className="flex h-20 items-center px-6">
          <div className="flex items-center space-x-4">
            <div className="w-8 h-8 bg-gradient-primary rounded-creative flex items-center justify-center text-white font-bold">
              S
            </div>
            <h1 className="text-2xl font-bold gradient-text">Creative Dashboard</h1>
          </div>
          <div className="ml-auto">
            <Button
              variant="accent"
              size="lg"
              onClick={handleCreateWorkflow}
              className="shadow-glow"
            >
              ✨ Create Workflow
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-8">
        <div className="mb-8">
          <h2 className="text-lg font-medium text-foreground mb-2">
            Welcome to your creative workspace
          </h2>
          <p className="text-muted-foreground">
            Manage all your AI-powered workflows, credentials, and creative executions in one place
          </p>
        </div>

        {/* Metrics Cards */}
        <MetricsGrid
          className="mb-10"
          activeMetric={activeMetric}
          onMetricClick={handleMetricClick}
        />

        {/* Navigation Tabs */}
        <NavigationTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-8"
        />

        {/* Welcome Section */}
        <WelcomeSection
          userName="Barun"
          onCreateWorkflow={handleCreateWorkflow}
        />
      </div>
    </div>
  )
}
