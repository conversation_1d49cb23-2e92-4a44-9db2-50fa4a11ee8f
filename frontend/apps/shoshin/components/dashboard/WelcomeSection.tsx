"use client"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface WelcomeSectionProps {
  userName?: string
  className?: string
  onCreateWorkflow?: () => void
}

export function WelcomeSection({ userName = "User", className, onCreateWorkflow }: WelcomeSectionProps) {
  return (
    <div className={cn("text-center py-20", className)}>
      <div className="mb-6">
        <span className="text-6xl">🎨</span>
      </div>
      <h2 className="text-3xl font-bold mb-4 gradient-text">
        Welcome {userName}!
      </h2>
      <p className="text-muted-foreground mb-12 text-lg max-w-md mx-auto">
        Ready to create something amazing? Start your first AI-powered creative workflow
      </p>

      {/* Start from scratch card */}
      <div className="max-w-md mx-auto">
        <Card
          className="bg-gradient-primary-soft hover:bg-gradient-primary-soft/80 cursor-pointer transition-all duration-300 border-2 border-dashed border-primary/30 hover:border-primary/60 hover:shadow-glow interactive-scale"
          onClick={onCreateWorkflow}
        >
          <CardContent className="p-10">
            <div className="mb-6">
              <div className="w-16 h-16 bg-gradient-primary rounded-creative-lg mx-auto flex items-center justify-center shadow-glow">
                <svg
                  className="w-8 h-8 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>
            </div>
            <div className="text-lg font-semibold text-foreground mb-2">
              Start from scratch
            </div>
            <div className="text-sm text-muted-foreground">
              Create a new workflow with AI-powered tools
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
