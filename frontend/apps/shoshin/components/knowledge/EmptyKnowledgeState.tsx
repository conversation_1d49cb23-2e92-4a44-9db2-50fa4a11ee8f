"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { BookOpen, Plus, Upload } from "lucide-react"

interface EmptyKnowledgeStateProps {
  onCreateClick: () => void
}

export function EmptyKnowledgeState({ onCreateClick }: EmptyKnowledgeStateProps) {
  return (
    <div className="text-center py-12 px-6 max-w-md mx-auto">
      {/* Icon */}
      <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
        <BookOpen className="w-8 h-8 text-purple-600 dark:text-purple-400" />
      </div>

      {/* Title */}
      <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
        Create your first knowledge base
      </h3>

      {/* Description */}
      <p className="text-gray-500 dark:text-gray-400 mb-6 leading-relaxed">
        Get started
      </p>
      <p className="text-gray-500 dark:text-gray-400 mb-8 leading-relaxed">
        Upload your documents to create a knowledge base for your agents.
      </p>

      {/* Action Button */}
      <Button 
        onClick={onCreateClick}
        className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2"
      >
        <Plus className="w-4 h-4 mr-2" />
        Create Knowledge Base
      </Button>

      {/* Additional Info */}
      <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Upload documents</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 flex items-center justify-center">
              <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
            </div>
            <span>Auto-chunking</span>
          </div>
          <div className="flex items-center space-x-2">
            <BookOpen className="w-4 h-4" />
            <span>Ready to use</span>
          </div>
        </div>
      </div>
    </div>
  )
}
