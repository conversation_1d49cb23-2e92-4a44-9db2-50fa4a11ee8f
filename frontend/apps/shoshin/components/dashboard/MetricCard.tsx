"use client"

import { Card, CardContent, CardDescription, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

interface MetricCardProps {
  title: string
  timeRange: string
  value: string | number
  unit?: string
  deviation?: number
  deviationUnit?: string
  className?: string
  isActive?: boolean
  onClick?: () => void
}

export function MetricCard({
  title,
  timeRange,
  value,
  unit,
  deviation,
  deviationUnit,
  className,
  isActive = false,
  onClick
}: MetricCardProps) {
  const getDeviationColor = (deviation: number) => {
    if (deviation > 0) return "text-success"
    if (deviation < 0) return "text-destructive"
    return "text-muted-foreground"
  }

  const getDeviationIcon = (deviation: number) => {
    if (deviation > 0) return "↗"
    if (deviation < 0) return "↘"
    return "→"
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Card
            className={cn(
              "cursor-pointer transition-all duration-300 hover:shadow-creative-lg interactive-scale",
              isActive && "ring-2 ring-primary shadow-glow border-primary/50",
              className
            )}
            onClick={onClick}
          >
            <CardHeader className="pb-3 space-y-2">
              <CardDescription className="text-xs font-medium text-muted-foreground uppercase tracking-wide">
                {title}
              </CardDescription>
              <CardDescription className="text-xs text-muted-foreground/70">
                {timeRange}
              </CardDescription>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex items-baseline space-x-3">
                <div className="text-3xl font-bold text-foreground">
                  {value}
                  {unit && <span className="text-base font-normal text-muted-foreground ml-1">{unit}</span>}
                </div>
                {deviation !== undefined && deviation !== null && (
                  <div className={cn("flex items-center text-sm font-medium", getDeviationColor(deviation))}>
                    <span className="mr-1">{getDeviationIcon(deviation)}</span>
                    <span>
                      {Math.abs(deviation)}{deviationUnit}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TooltipTrigger>
        <TooltipContent>
          <p>Click to filter by {title.toLowerCase()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

interface MetricsGridProps {
  className?: string
  activeMetric?: string
  onMetricClick?: (metric: string) => void
}

export function MetricsGrid({ className, activeMetric, onMetricClick }: MetricsGridProps) {
  const metrics = [
    {
      id: "total",
      title: "Creative Projects",
      timeRange: "Last 7 days",
      value: 12,
      deviation: 3
    },
    {
      id: "failed",
      title: "AI Generations",
      timeRange: "Last 7 days",
      value: 847,
      deviation: 156
    },
    {
      id: "failureRate",
      title: "Success Rate",
      timeRange: "Last 7 days",
      value: 98.2,
      unit: "%",
      deviation: 2.1
    },
    {
      id: "timeSaved",
      title: "Time Saved",
      timeRange: "Last 7 days",
      value: 24,
      unit: "hrs",
      deviation: 8
    },
    {
      id: "runtime",
      title: "Avg. Generation",
      timeRange: "Last 7 days",
      value: 3.2,
      unit: "s",
      deviation: -0.5
    }
  ]

  return (
    <div className={cn("grid grid-cols-1 md:grid-cols-5 gap-4", className)}>
      {metrics.map((metric) => (
        <MetricCard
          key={metric.id}
          title={metric.title}
          timeRange={metric.timeRange}
          value={metric.value}
          unit={metric.unit}
          deviation={metric.deviation}
          isActive={activeMetric === metric.id}
          onClick={() => onMetricClick?.(metric.id)}
        />
      ))}
    </div>
  )
}
