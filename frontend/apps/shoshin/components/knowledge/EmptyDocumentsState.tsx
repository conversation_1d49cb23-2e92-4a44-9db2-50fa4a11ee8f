"use client"

import { But<PERSON> } from "@/components/ui/button"
import { FileText, Upload, Plus } from "lucide-react"

interface EmptyDocumentsStateProps {
  onUploadClick: () => void
}

export function EmptyDocumentsState({ onUploadClick }: EmptyDocumentsStateProps) {
  return (
    <div className="flex-1 flex items-center justify-center p-12">
      <div className="text-center max-w-md mx-auto">
        {/* Icon */}
        <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-6">
          <FileText className="w-8 h-8 text-gray-400" />
        </div>

        {/* Title */}
        <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          No documents yet
        </h3>

        {/* Description */}
        <p className="text-gray-500 dark:text-gray-400 mb-6 leading-relaxed">
          Upload your first documents to get started with this knowledge base.
        </p>

        {/* Action Button */}
        <Button 
          onClick={onUploadClick}
          className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Documents
        </Button>

        {/* Additional Info */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-2">
              <Upload className="w-4 h-4" />
              <span>Drag & drop</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 flex items-center justify-center">
                <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
              </div>
              <span>Multiple formats</span>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Auto-chunked</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
