"use client"

import { MetricsGrid } from "@/components/dashboard/MetricCard"
import { NavigationTabs } from "@/components/dashboard/NavigationTabs"
import { WelcomeSection } from "@/components/dashboard/WelcomeSection"
import { Button } from "@/components/ui/button"
import { useRouter } from "next/navigation"
import { useState } from "react"

export default function DashboardPage() {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("workflows")
  const [activeMetric, setActiveMetric] = useState<string>()

  const tabs = [
    { id: "workflows", label: "Workflows", count: 0 },
    { id: "credentials", label: "Credentials", count: 0 },
    { id: "executions", label: "Executions", count: 0 }
  ]

  const handleCreateWorkflow = () => {
    router.push("/editor")
  }

  const handleMetricClick = (metric: string) => {
    setActiveMetric(metric)
    // Add metric filtering logic here
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="border-b bg-white dark:bg-gray-800">
        <div className="flex h-16 items-center px-6">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Overview</h1>
          </div>
          <div className="ml-auto">
            <Button
              className="bg-orange-500 hover:bg-orange-600 text-white"
              onClick={handleCreateWorkflow}
            >
              Create Workflow
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto p-6">
        <div className="mb-6">
          <p className="text-gray-600 dark:text-gray-400">
            All the workflows, credentials and executions you have access to
          </p>
        </div>

        {/* Metrics Cards */}
        <MetricsGrid
          className="mb-8"
          activeMetric={activeMetric}
          onMetricClick={handleMetricClick}
        />

        {/* Navigation Tabs */}
        <NavigationTabs
          tabs={tabs}
          activeTab={activeTab}
          onTabChange={setActiveTab}
          className="mb-6"
        />

        {/* Welcome Section */}
        <WelcomeSection
          userName="Barun"
          onCreateWorkflow={handleCreateWorkflow}
        />
      </div>
    </div>
  )
}
