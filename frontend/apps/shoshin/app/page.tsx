"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto py-20 px-4">
        {/* Hero Section */}
        <div className="text-center space-y-8 mb-16">
          <Badge variant="accent" className="mb-4">
            ✨ AI-Powered Creative Studio
          </Badge>
          <h1 className="text-6xl font-bold gradient-text leading-tight">
            Welcome to Shoshin
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            Create stunning product photoshoots and ad campaign posters with the power of AI.
            Transform your marketing vision into reality with our creative tools.
          </p>
          <div className="flex gap-6 justify-center items-center flex-wrap">
            <Button size="lg" className="text-lg">
              🚀 Get Started
            </Button>
            <Button variant="outline" size="lg" className="text-lg">
              📖 Learn More
            </Button>
            <Button variant="accent" size="lg" className="text-lg">
              🎨 View Gallery
            </Button>
          </div>
        </div>

        {/* Feature Cards */}
        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-gradient-primary rounded-creative-lg mx-auto mb-4 flex items-center justify-center text-2xl">
                📸
              </div>
              <CardTitle className="gradient-text">Product Photoshoots</CardTitle>
              <CardDescription>
                Generate professional product images with AI-powered photography
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="ghost" className="w-full">
                Explore →
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-gradient-accent rounded-creative-lg mx-auto mb-4 flex items-center justify-center text-2xl">
                🎯
              </div>
              <CardTitle className="gradient-text">Ad Campaigns</CardTitle>
              <CardDescription>
                Create compelling marketing materials and campaign visuals
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="ghost" className="w-full">
                Create →
              </Button>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <div className="w-16 h-16 bg-secondary rounded-creative-lg mx-auto mb-4 flex items-center justify-center text-2xl">
                ⚡
              </div>
              <CardTitle className="gradient-text">AI-Powered</CardTitle>
              <CardDescription>
                Leverage cutting-edge AI technology for creative excellence
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button variant="ghost" className="w-full">
                Discover →
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-20">
          <Card className="max-w-2xl mx-auto bg-gradient-primary-soft border-primary/20">
            <CardHeader>
              <CardTitle className="text-2xl">Ready to Create?</CardTitle>
              <CardDescription className="text-lg">
                Join thousands of creators and marketers using Shoshin to bring their visions to life
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button size="xl" variant="default" className="w-full md:w-auto">
                Start Creating Now 🎨
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}