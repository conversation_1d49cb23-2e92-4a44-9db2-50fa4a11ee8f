"use client";

import type { <PERSON>, Node } from "@xyflow/react";
import { useCallback, useRef, useState } from "react";

interface HistoryState {
  nodes: Node[];
  edges: Edge[];
  timestamp: number;
}

interface HistoryActions {
  undo: () => boolean;
  redo: () => boolean;
  canUndo: boolean;
  canRedo: boolean;
  saveState: (nodes: Node[], edges: Edge[]) => void;
  clearHistory: () => void;
}

const MAX_HISTORY_SIZE = 50;

export function useHistory(
  setNodes: (nodes: Node[]) => void,
  setEdges: (edges: Edge[]) => void,
): HistoryActions {
  const undoStack = useRef<HistoryState[]>([]);
  const redoStack = useRef<HistoryState[]>([]);
  const [canUndo, setCanUndo] = useState(false);
  const [canRedo, setCanRedo] = useState(false);

  // Store the setters to avoid dependency issues
  const setNodesRef = useRef(setNodes);
  const setEdgesRef = useRef(setEdges);

  // Update refs when setters change
  setNodesRef.current = setNodes;
  setEdgesRef.current = setEdges;

  const updateFlags = useCallback(() => {
    setCanUndo(undoStack.current.length > 0);
    setCanRedo(redoStack.current.length > 0);
  }, []);

  const saveState = useCallback(
    (currentNodes: Node[], currentEdges: Edge[]) => {
      // Deep clone the current state
      const state: HistoryState = {
        nodes: JSON.parse(JSON.stringify(currentNodes)),
        edges: JSON.parse(JSON.stringify(currentEdges)),
        timestamp: Date.now(),
      };

      undoStack.current.push(state);

      // Limit history size
      if (undoStack.current.length > MAX_HISTORY_SIZE) {
        undoStack.current.shift();
      }

      // Clear redo stack when new action is performed
      redoStack.current = [];
      updateFlags();
    },
    [updateFlags],
  );

  const undo = useCallback((): boolean => {
    if (undoStack.current.length === 0) return false;

    // Get the previous state
    const previousState = undoStack.current.pop()!;

    // Restore previous state
    setNodesRef.current(previousState.nodes);
    setEdgesRef.current(previousState.edges);

    updateFlags();
    return true;
  }, [updateFlags]);

  const redo = useCallback((): boolean => {
    if (redoStack.current.length === 0) return false;

    // Get the next state
    const nextState = redoStack.current.pop()!;

    // Restore next state
    setNodesRef.current(nextState.nodes);
    setEdgesRef.current(nextState.edges);

    updateFlags();
    return true;
  }, [updateFlags]);

  const clearHistory = useCallback(() => {
    undoStack.current = [];
    redoStack.current = [];
    updateFlags();
  }, [updateFlags]);

  return {
    undo,
    redo,
    canUndo,
    canRedo,
    saveState,
    clearHistory,
  };
}
