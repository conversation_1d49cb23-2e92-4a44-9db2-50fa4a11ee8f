"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useKnowledgeStore } from "@/stores/knowledgeStore"
import { CreateKnowledgeBaseModal } from "@/components/knowledge/CreateKnowledgeBaseModal"
import { EmptyKnowledgeState } from "@/components/knowledge/EmptyKnowledgeState"
import Link from "next/link"
import { Search, Plus, BookOpen, FileText, Clock } from "lucide-react"

export default function KnowledgePage() {
  const {
    getAllKnowledgeBases,
    searchKnowledgeBases,
    searchQuery,
    setSearchQuery,
    isCreateModalOpen,
    openCreateModal,
    closeCreateModal
  } = useKnowledgeStore()

  const [localSearchQuery, setLocalSearchQuery] = useState(searchQuery)
  
  const allKnowledgeBases = getAllKnowledgeBases()
  const filteredKnowledgeBases = localSearchQuery 
    ? searchKnowledgeBases(localSearchQuery)
    : allKnowledgeBases

  const handleSearch = (query: string) => {
    setLocalSearchQuery(query)
    setSearchQuery(query)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getDocumentStats = (knowledgeBase: any) => {
    const totalDocs = knowledgeBase.documents.length
    const readyDocs = knowledgeBase.documents.filter((doc: any) => doc.status === 'ready').length
    const processingDocs = knowledgeBase.documents.filter((doc: any) => doc.status === 'processing').length
    
    return { totalDocs, readyDocs, processingDocs }
  }

  if (allKnowledgeBases.length === 0) {
    return (
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <BookOpen className="w-6 h-6 text-purple-500" />
            <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Knowledge</h1>
          </div>
          <Button onClick={openCreateModal} className="bg-purple-600 hover:bg-purple-700">
            <Plus className="w-4 h-4 mr-2" />
            Create
          </Button>
        </div>

        {/* Empty State */}
        <div className="flex-1 flex items-center justify-center">
          <EmptyKnowledgeState onCreateClick={openCreateModal} />
        </div>

        {/* Create Modal */}
        <CreateKnowledgeBaseModal 
          isOpen={isCreateModalOpen}
          onClose={closeCreateModal}
        />
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="flex items-center justify-between p-6 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-3">
          <BookOpen className="w-6 h-6 text-purple-500" />
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-white">Knowledge</h1>
        </div>
        <Button onClick={openCreateModal} className="bg-purple-600 hover:bg-purple-700">
          <Plus className="w-4 h-4 mr-2" />
          Create
        </Button>
      </div>

      {/* Search Bar */}
      <div className="p-6 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search knowledge bases..."
            value={localSearchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10 bg-gray-50 dark:bg-gray-700 border-gray-200 dark:border-gray-600"
          />
        </div>
      </div>

      {/* Knowledge Bases Grid */}
      <div className="flex-1 p-6 overflow-auto">
        {filteredKnowledgeBases.length === 0 ? (
          <div className="text-center py-12">
            <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No knowledge bases found
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Try adjusting your search terms or create a new knowledge base.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredKnowledgeBases.map((knowledgeBase) => {
              const stats = getDocumentStats(knowledgeBase)
              
              return (
                <Link key={knowledgeBase.id} href={`/dashboard/knowledge/${knowledgeBase.id}`}>
                  <Card className="h-full hover:shadow-lg transition-shadow cursor-pointer border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                            {knowledgeBase.name}
                          </CardTitle>
                          {knowledgeBase.description && (
                            <CardDescription className="mt-1 text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
                              {knowledgeBase.description}
                            </CardDescription>
                          )}
                        </div>
                        <div className="ml-2 flex-shrink-0">
                          <Badge variant="secondary" className="text-xs">
                            {stats.totalDocs} docs
                          </Badge>
                        </div>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-0">
                      <div className="space-y-3">
                        {/* Document Status */}
                        <div className="flex items-center space-x-4 text-sm">
                          <div className="flex items-center space-x-1">
                            <FileText className="w-4 h-4 text-green-500" />
                            <span className="text-gray-600 dark:text-gray-300">
                              {stats.readyDocs} ready
                            </span>
                          </div>
                          {stats.processingDocs > 0 && (
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
                              <span className="text-gray-600 dark:text-gray-300">
                                {stats.processingDocs} processing
                              </span>
                            </div>
                          )}
                        </div>

                        {/* Chunking Configuration */}
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Chunk: {knowledgeBase.minChunkSize}-{knowledgeBase.maxChunkSize} chars
                          {knowledgeBase.overlapSize > 0 && `, ${knowledgeBase.overlapSize} overlap`}
                        </div>

                        {/* Last Updated */}
                        <div className="flex items-center space-x-1 text-xs text-gray-500 dark:text-gray-400">
                          <Clock className="w-3 h-3" />
                          <span>Updated {formatDate(knowledgeBase.updatedAt)}</span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </Link>
              )
            })}
          </div>
        )}
      </div>

      {/* Create Modal */}
      <CreateKnowledgeBaseModal 
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
      />
    </div>
  )
}
