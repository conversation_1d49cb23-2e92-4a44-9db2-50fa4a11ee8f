"use client"

import { useCallback, useState } from "react"
import type { Node, Edge } from "@xyflow/react"

export interface SelectionState {
  nodes: Node[]
  edges: Edge[]
}

export function useSelection() {
  const [selectedNodes, setSelectedNodes] = useState<Node[]>([])
  const [selectedEdges, setSelectedEdges] = useState<Edge[]>([])

  const updateSelection = useCallback((nodes: Node[], edges: Edge[]) => {
    const selected = nodes.filter(node => node.selected)
    const selectedEdgeIds = edges.filter(edge => edge.selected)
    
    setSelectedNodes(selected)
    setSelectedEdges(selectedEdgeIds)
  }, [])

  const clearSelection = useCallback(() => {
    setSelectedNodes([])
    setSelectedEdges([])
  }, [])

  const hasSelection = selectedNodes.length > 0 || selectedEdges.length > 0

  const getSelection = useCallback((): SelectionState => ({
    nodes: selectedNodes,
    edges: selectedEdges
  }), [selectedNodes, selectedEdges])

  return {
    selectedNodes,
    selectedEdges,
    hasSelection,
    updateSelection,
    clearSelection,
    getSelection
  }
}
