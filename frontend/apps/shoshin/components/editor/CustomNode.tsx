"use client"

import { <PERSON><PERSON>, Position, type NodeProps } from "@xyflow/react"
import {
    Brain,
    Code,
    Database,
    GitBranch,
    Globe,
    MessageSquare,
    Play,
    RotateCcw,
    Route,
    User,
    Workflow
} from "lucide-react"

const iconMap = {
  agent: User,
  api: Globe,
  condition: GitBranch,
  function: Code,
  router: Route,
  memory: Database,
  knowledge: Brain,
  workflow: Workflow,
  response: MessageSquare,
  loop: RotateCcw,
  start: Play,
}

const colorMap = {
  agent: "bg-purple-600",
  api: "bg-blue-600",
  condition: "bg-orange-600",
  function: "bg-red-600",
  router: "bg-green-600",
  memory: "bg-pink-600",
  knowledge: "bg-teal-600",
  workflow: "bg-amber-600",
  response: "bg-blue-500",
  loop: "bg-cyan-600",
  start: "bg-gray-600",
}

interface CustomNodeData {
  label: string
  type: string
  description?: string
}

export function CustomNode({ data, selected }: NodeProps<CustomNodeData>) {
  const Icon = iconMap[data.type as keyof typeof iconMap] || Play
  const bgColor = colorMap[data.type as keyof typeof colorMap] || "bg-gray-600"

  return (
    <div className={`px-4 py-3 shadow-lg rounded-lg bg-gray-800 border-2 min-w-[200px] transition-all hover:shadow-xl ${
      selected ? "border-purple-500 shadow-purple-500/20" : "border-gray-600 hover:border-gray-500"
    }`}>
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-gray-400 border-2 border-gray-600 hover:!bg-purple-500 hover:border-purple-400 transition-colors"
      />

      <div className="flex items-center space-x-3">
        <div className={`w-8 h-8 ${bgColor} rounded flex items-center justify-center flex-shrink-0 shadow-sm`}>
          <Icon className="w-4 h-4 text-white" />
        </div>

        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-white truncate">
            {data.label}
          </div>
          {data.description && (
            <div className="text-xs text-gray-400 truncate">
              {data.description}
            </div>
          )}
        </div>
      </div>

      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-gray-400 border-2 border-gray-600 hover:!bg-purple-500 hover:border-purple-400 transition-colors"
      />
    </div>
  )
}
