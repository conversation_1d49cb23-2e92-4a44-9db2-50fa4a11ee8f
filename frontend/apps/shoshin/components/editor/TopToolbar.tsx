"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  MessageSquare,
  MoreHorizontal,
  Network,
  Play,
  Save,
  Search,
  Settings,
  Share2,
  User
} from "lucide-react"
import { useEffect } from "react"

export function TopToolbar() {
  const handleAutoLayout = () => {
    window.dispatchEvent(new CustomEvent('trigger-auto-layout'))
    // Optional: Add visual feedback
    console.log('Auto Layout triggered')
  }

  // Keyboard shortcut for Auto Layout (Shift+L)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.shiftKey && event.key.toLowerCase() === 'l') {
        event.preventDefault()
        handleAutoLayout()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => {
      window.removeEventListener('keydown', handleKeyDown)
    }
  }, [])
  return (
    <div className="h-14 bg-gray-800 border-b border-gray-700 flex items-center px-4 justify-between" style={{ paddingLeft: '96px' }}>
      {/* Left Section */}
      <div className="flex items-center space-x-4">
        {/* User Avatar */}
        <div className="w-8 h-8 bg-purple-600 rounded-full flex items-center justify-center">
          <User className="w-4 h-4 text-white" />
        </div>
        
        {/* Project Info */}
        <div className="text-white">
          <div className="text-sm font-medium">ambar-aurora</div>
          <div className="text-xs text-gray-400">Saved about 16 hours ago</div>
        </div>
      </div>

      {/* Center Section - Search */}
      <div className="flex-1 max-w-md mx-8">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search..."
            className="w-full bg-gray-700 text-white placeholder-gray-400 pl-10 pr-4 py-2 rounded-md border border-gray-600 focus:border-purple-500 focus:outline-none"
          />
        </div>
      </div>

      {/* Right Section */}
      <div className="flex items-center space-x-2">
        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <Save className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <Share2 className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <MessageSquare className="w-4 h-4" />
        </Button>
        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <Settings className="w-4 h-4" />
        </Button>

        {/* Auto Layout Button */}
        <Button
          variant="ghost"
          size="sm"
          className="text-gray-300 hover:text-white"
          title="Auto Layout (Shift+L)"
          onClick={handleAutoLayout}
        >
          <Network className="w-4 h-4 mr-2" />
          Auto Layout
        </Button>

        <Button variant="ghost" size="sm" className="text-gray-300 hover:text-white">
          <MoreHorizontal className="w-4 h-4" />
        </Button>

        {/* Debug Button */}
        <Button
          size="sm"
          className="bg-purple-600 hover:bg-purple-700 text-white px-4"
        >
          <Play className="w-4 h-4 mr-2" />
          Debug
        </Button>
      </div>
    </div>
  )
}
