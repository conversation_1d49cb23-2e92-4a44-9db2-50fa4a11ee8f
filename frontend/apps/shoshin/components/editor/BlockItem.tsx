"use client"

import { LucideIcon } from "lucide-react"

interface BlockItemProps {
  id: string
  name: string
  description: string
  icon: LucideIcon
  color: string
}

export function BlockItem({ id, name, description, icon: Icon, color }: BlockItemProps) {
  const handleDragStart = (e: React.DragEvent) => {
    e.dataTransfer.setData("application/reactflow", JSON.stringify({
      type: id,
      name,
      description
    }))
    e.dataTransfer.effectAllowed = "move"
  }

  return (
    <div
      draggable
      onDragStart={handleDragStart}
      className="flex items-center p-3 rounded-lg bg-gray-700 hover:bg-gray-600 cursor-grab active:cursor-grabbing transition-colors group"
    >
      {/* Icon */}
      <div className={`w-8 h-8 ${color} rounded flex items-center justify-center mr-3 flex-shrink-0`}>
        <Icon className="w-4 h-4 text-white" />
      </div>
      
      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="text-sm font-medium text-white group-hover:text-gray-100">
          {name}
        </div>
        {description && (
          <div className="text-xs text-gray-400 group-hover:text-gray-300 truncate">
            {description}
          </div>
        )}
      </div>
    </div>
  )
}
