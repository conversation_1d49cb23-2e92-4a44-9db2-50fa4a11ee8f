import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import * as React from "react"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-creative text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-gradient-primary text-primary-foreground shadow-creative hover:shadow-glow hover:scale-105 active:scale-95",
        destructive:
          "bg-destructive text-destructive-foreground shadow-creative hover:bg-destructive/90 hover:shadow-lg",
        outline:
          "border border-input bg-background shadow-creative hover:bg-gradient-primary-soft hover:border-primary/50 hover:shadow-glow",
        secondary:
          "bg-secondary text-secondary-foreground shadow-creative hover:bg-secondary/80 hover:shadow-glow-secondary",
        ghost: "hover:bg-gradient-primary-soft hover:text-primary transition-all duration-200",
        link: "text-primary underline-offset-4 hover:underline hover:text-primary-light",
        accent: "bg-gradient-accent text-white shadow-creative hover:shadow-lg hover:scale-105 active:scale-95",
        success: "bg-success text-success-foreground shadow-creative hover:bg-success/90 hover:shadow-lg",
        warning: "bg-warning text-warning-foreground shadow-creative hover:bg-warning/90 hover:shadow-lg",
      },
      size: {
        default: "h-10 px-6 py-2",
        sm: "h-8 rounded-creative-sm px-4 text-xs",
        lg: "h-12 rounded-creative-lg px-8 text-base",
        icon: "h-10 w-10",
        xl: "h-14 rounded-creative-xl px-10 text-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
